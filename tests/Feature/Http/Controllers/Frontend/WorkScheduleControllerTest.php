<?php

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleAssignmentType;
use App\Enums\WorkScheduleType;
use App\Jobs\GenerateWorkScheduleRecordsJob;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use App\Models\Workday;
use App\Models\WorkSchedule;
use App\Models\WorkScheduleAssignment;
use Illuminate\Support\Facades\Queue;
use function Pest\Laravel\assertDatabaseCount;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

test('index returns paginated work schedules', function () {
    $workSchedules = WorkSchedule::factory()
        ->count(3)
        ->for($this->tenant)
        ->create();

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->get('api/v1/frontend/work-schedules')
        ->assertOk();

    $response->assertJsonCount(3, 'data.data');
    $response->assertJsonStructure([
        'data' => [
            'data' => [
                '*' => [
                    'id',
                    'type',
                    'name',
                    'work_and_off_days_distribution_type',
                    'specific_days',
                    'off_days_after_each_repetition',
                    'start_date',
                    'created_at',
                    'updated_at',
                ],
            ],
            'links',
            'meta',
        ],
    ]);
});

test('index filters work schedules by search term', function () {
    WorkSchedule::factory()
        ->for($this->tenant)
        ->create(['name' => 'Morning Schedule']);

    WorkSchedule::factory()
        ->for($this->tenant)
        ->create(['name' => 'Evening Schedule']);

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->get('api/v1/frontend/work-schedules?filter[search]=Morning')
        ->assertOk();

    $response->assertJsonCount(1, 'data.data');
    $response->assertJsonPath('data.data.0.name', 'Morning Schedule');
});

test('index filters work schedules by type', function () {
    WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->create(['name' => 'Fixed Schedule']);

    WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->create(['name' => 'Rotational Schedule']);

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->get('api/v1/frontend/work-schedules?filter[type]=fixed')
        ->assertOk();

    $response->assertJsonCount(1, 'data.data');
    $response->assertJsonPath('data.data.0.type', 'fixed');
});

test('show returns a specific work schedule', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->create();

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->get("api/v1/frontend/work-schedules/{$workSchedule->id}")
        ->assertOk();

    $response->assertJsonStructure([
        'data' => [
            'id',
            'type',
            'name',
            'work_and_off_days_distribution_type',
            'specific_days',
            'off_days_after_each_repetition',
            'start_date',
            'created_at',
            'updated_at',
        ],
    ]);
    $response->assertJsonPath('data.id', $workSchedule->id);
});

test('store creates fixed work schedule with number of days distribution', function () {
    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'Test Fixed Schedule',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::NumberOfDays->value,
        'off_days_after_each_repetition' => 2,
        'start_date' => now()->addDays(7)->format('Y-m-d'),
        'workdays' => [
            [
                'workday_id' => $workday->id,
                'work_days_number' => 5,
                'off_days_number' => 2,
            ],
        ],
    ];

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->postJson('api/v1/frontend/work-schedules', $data)
        ->assertOk();

    assertDatabaseHas('work_schedules', [
        'team_id' => $this->tenant->id,
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'Test Fixed Schedule',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::NumberOfDays->value,
        'off_days_after_each_repetition' => 2,
    ]);

    assertDatabaseHas('workday_work_schedule', [
        'workday_id' => $workday->id,
        'work_days_number' => 5,
        'off_days_number' => 2,
    ]);
});

test('store creates fixed work schedule with specific days distribution', function () {
    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'Test Specific Days Schedule',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::SpecificDays->value,
        'specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        'start_date' => now()->addDays(7)->format('Y-m-d'),
        'workdays' => [
            [
                'workday_id' => $workday->id,
            ],
        ],
    ];

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->postJson('api/v1/frontend/work-schedules', $data)
        ->assertOk();

    assertDatabaseHas('work_schedules', [
        'team_id' => $this->tenant->id,
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'Test Specific Days Schedule',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::SpecificDays->value,
    ]);

    $workSchedule = WorkSchedule::where('name', 'Test Specific Days Schedule')->first();
    expect($workSchedule->specific_days)->toEqual([
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
    ]);
});

test('store creates rotational work schedule', function () {
    $workday1 = Workday::factory()
        ->for($this->tenant)
        ->create();
    $workday2 = Workday::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Rotational->value,
        'name' => 'Test Rotational Schedule',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::NumberOfDays->value,
        'off_days_after_each_repetition' => 1,
        'start_date' => now()->addDays(7)->format('Y-m-d'),
        'workdays' => [
            [
                'workday_id' => $workday1->id,
                'work_days_number' => 5,
                'off_days_number' => 2,
                'repetitions_number' => 4,
            ],
            [
                'workday_id' => $workday2->id,
                'work_days_number' => 3,
                'off_days_number' => 1,
                'repetitions_number' => 2,
            ],
        ],
    ];

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->postJson('api/v1/frontend/work-schedules', $data)
        ->assertOk();

    assertDatabaseHas('work_schedules', [
        'team_id' => $this->tenant->id,
        'type' => WorkScheduleType::Rotational->value,
        'name' => 'Test Rotational Schedule',
    ]);

    assertDatabaseCount('workday_work_schedule', 2);
});

test('store validation requires all required fields', function () {
    $this->jwtActingAsAttendanceHR($this->user)
        ->postJson('api/v1/frontend/work-schedules', [])
        ->assertStatus(422)
        ->assertJsonValidationErrors([
            'type',
            'name',
            'work_and_off_days_distribution_type',
            'start_date',
            'workdays',
        ]);
});

test('store validation requires specific days when using specific days distribution', function () {
    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'Test Schedule',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::SpecificDays->value,
        'start_date' => now()->addDays(7)->format('Y-m-d'),
        'workdays' => [['workday_id' => $workday->id]],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->postJson('api/v1/frontend/work-schedules', $data)
        ->assertStatus(422)
        ->assertJsonValidationErrors(['specific_days']);
});

test('store validation requires off days when using number of days distribution', function () {
    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'Test Schedule',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::NumberOfDays->value,
        'start_date' => now()->addDays(7)->format('Y-m-d'),
        'workdays' => [['workday_id' => $workday->id]],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->postJson('api/v1/frontend/work-schedules', $data)
        ->assertStatus(422)
        ->assertJsonValidationErrors([
            'off_days_after_each_repetition',
            'workdays.0.work_days_number',
            'workdays.0.off_days_number',
        ]);
});

test('store validation requires repetitions for rotational schedules', function () {
    $workday1 = Workday::factory()
        ->for($this->tenant)
        ->create();
    $workday2 = Workday::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Rotational->value,
        'name' => 'Test Schedule',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::NumberOfDays->value,
        'off_days_after_each_repetition' => 1,
        'start_date' => now()->addDays(7)->format('Y-m-d'),
        'workdays' => [
            [
                'workday_id' => $workday1->id,
                'work_days_number' => 5,
                'off_days_number' => 2,
            ],
            [
                'workday_id' => $workday2->id,
                'work_days_number' => 3,
                'off_days_number' => 1,
            ],
        ],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->postJson('api/v1/frontend/work-schedules', $data)
        ->assertStatus(422)
        ->assertJsonValidationErrors([
            'workdays.0.repetitions_number',
            'workdays.1.repetitions_number',
        ]);
});

test('update work schedule with valid data', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->create();

    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'Updated Schedule',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::NumberOfDays->value,
        'off_days_after_each_repetition' => 3,
        'start_date' => now()->addDays(14)->format('Y-m-d'),
        'workdays' => [
            [
                'workday_id' => $workday->id,
                'work_days_number' => 6,
                'off_days_number' => 1,
            ],
        ],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/work-schedules/{$workSchedule->id}", $data)
        ->assertOk();

    assertDatabaseHas('work_schedules', [
        'id' => $workSchedule->id,
        'name' => 'Updated Schedule',
        'off_days_after_each_repetition' => 3,
    ]);
});

test('destroy deletes a work schedule', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->delete("api/v1/frontend/work-schedules/{$workSchedule->id}")
        ->assertOk();

    assertDatabaseMissing('work_schedules', ['id' => $workSchedule->id]);
});

test('destroy returns not found for non-existent work schedule', function () {
    $this->jwtActingAsAttendanceHR($this->user)
        ->delete('api/v1/frontend/work-schedules/999')
        ->assertNotFound();
});

test('destroy returns not found for work schedule from another tenant', function () {
    $anotherTeam = Team::factory()->create();
    $workSchedule = WorkSchedule::factory()->for($anotherTeam)->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->delete("api/v1/frontend/work-schedules/{$workSchedule->id}")
        ->assertNotFound();
});

test('dashboard viewer can access index endpoint', function () {
    WorkSchedule::factory()
        ->count(3)
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsAttendanceDashboardViewer($this->user)
        ->get('api/v1/frontend/work-schedules')
        ->assertOk()
        ->assertJsonCount(3, 'data.data');
});

test('dashboard viewer cannot access other endpoints', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->create();

    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'New Schedule',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::NumberOfDays->value,
        'off_days_after_each_repetition' => 2,
        'start_date' => now()->addDays(7)->format('Y-m-d'),
        'workdays' => [
            [
                'workday_id' => $workday->id,
                'work_days_number' => 5,
                'off_days_number' => 2,
            ],
        ],
    ];

    $this->jwtActingAsAttendanceDashboardViewer($this->user)
        ->postJson('api/v1/frontend/work-schedules', $data)
        ->assertForbidden();

    $this->jwtActingAsAttendanceDashboardViewer($this->user)
        ->get("api/v1/frontend/work-schedules/{$workSchedule->id}")
        ->assertForbidden();

    $this->jwtActingAsAttendanceDashboardViewer($this->user)
        ->putJson("api/v1/frontend/work-schedules/{$workSchedule->id}", $data)
        ->assertForbidden();

    $this->jwtActingAsAttendanceDashboardViewer($this->user)
        ->delete("api/v1/frontend/work-schedules/{$workSchedule->id}")
        ->assertForbidden();
});

test('store creates work schedule with employee assignments', function () {
    Queue::fake();

    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();
    $employee1 = Employee::factory()
        ->for($this->tenant)
        ->create();
    $employee2 = Employee::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'Test Schedule with Assignments',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::NumberOfDays->value,
        'off_days_after_each_repetition' => 2,
        'start_date' => now()->addDays(7)->format('Y-m-d'),
        'workdays' => [
            [
                'workday_id' => $workday->id,
                'work_days_number' => 5,
                'off_days_number' => 2,
            ],
        ],
        'assignments' => [
            'employees' => [$employee1->id, $employee2->id],
        ],
    ];

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->postJson('api/v1/frontend/work-schedules', $data)
        ->assertOk();

    $workSchedule = WorkSchedule::where('name', 'Test Schedule with Assignments')->first();

    assertDatabaseHas('work_schedule_assignments', [
        'work_schedule_id' => $workSchedule->id,
        'type' => WorkScheduleAssignmentType::Employee->value,
    ]);

    Queue::assertPushed(GenerateWorkScheduleRecordsJob::class);
});

test('store creates work schedule with department assignments', function () {
    Queue::fake();

    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();
    $department = Department::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'Test Schedule with Department',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::NumberOfDays->value,
        'off_days_after_each_repetition' => 2,
        'start_date' => now()->addDays(7)->format('Y-m-d'),
        'workdays' => [
            [
                'workday_id' => $workday->id,
                'work_days_number' => 5,
                'off_days_number' => 2,
            ],
        ],
        'assignments' => [
            'departments' => [$department->id],
        ],
    ];

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->postJson('api/v1/frontend/work-schedules', $data)
        ->assertOk();

    $workSchedule = WorkSchedule::where('name', 'Test Schedule with Department')->first();

    assertDatabaseHas('work_schedule_assignments', [
        'work_schedule_id' => $workSchedule->id,
        'type' => WorkScheduleAssignmentType::Department->value,
    ]);

    Queue::assertPushed(GenerateWorkScheduleRecordsJob::class);
});

test('store creates work schedule with multiple assignment types', function () {
    Queue::fake();

    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();
    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();
    $department = Department::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'Test Schedule with Multiple Assignments',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::NumberOfDays->value,
        'off_days_after_each_repetition' => 2,
        'start_date' => now()->addDays(7)->format('Y-m-d'),
        'workdays' => [
            [
                'workday_id' => $workday->id,
                'work_days_number' => 5,
                'off_days_number' => 2,
            ],
        ],
        'assignments' => [
            'employees' => [$employee->id],
            'departments' => [$department->id],
        ],
    ];

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->postJson('api/v1/frontend/work-schedules', $data)
        ->assertOk();

    $workSchedule = WorkSchedule::where('name', 'Test Schedule with Multiple Assignments')->first();

    assertDatabaseCount('work_schedule_assignments', 2);

    assertDatabaseHas('work_schedule_assignments', [
        'work_schedule_id' => $workSchedule->id,
        'type' => WorkScheduleAssignmentType::Employee->value,
    ]);

    assertDatabaseHas('work_schedule_assignments', [
        'work_schedule_id' => $workSchedule->id,
        'type' => WorkScheduleAssignmentType::Department->value,
    ]);

    Queue::assertPushed(GenerateWorkScheduleRecordsJob::class);
});

test('update work schedule replaces assignments', function () {
    Queue::fake();

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->create();

    // Create initial assignment
    WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->for($workSchedule)
        ->employee()
        ->create();

    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();
    $department = Department::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'type' => WorkScheduleType::Fixed->value,
        'name' => 'Updated Schedule',
        'work_and_off_days_distribution_type' =>
            WorkAndOffDaysDistributionType::NumberOfDays->value,
        'off_days_after_each_repetition' => 3,
        'start_date' => now()->addDays(14)->format('Y-m-d'),
        'workdays' => [
            [
                'workday_id' => $workday->id,
                'work_days_number' => 6,
                'off_days_number' => 1,
            ],
        ],
        'assignments' => [
            'departments' => [$department->id],
        ],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/work-schedules/{$workSchedule->id}", $data)
        ->assertOk();

    // Should have only one assignment (department), the employee assignment should be replaced
    assertDatabaseCount('work_schedule_assignments', 1);

    assertDatabaseHas('work_schedule_assignments', [
        'work_schedule_id' => $workSchedule->id,
        'type' => WorkScheduleAssignmentType::Department->value,
    ]);

    Queue::assertPushed(GenerateWorkScheduleRecordsJob::class);
});

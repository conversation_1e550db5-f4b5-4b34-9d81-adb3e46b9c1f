<?php

namespace App\Console\Commands;

use App\Jobs\GenerateWorkScheduleRecordsJob;
use App\Models\WorkSchedule;
use Illuminate\Console\Command;

class GenerateWorkScheduleRecordsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'work-schedule:generate-records';

    /**
     * The console command description.
     */
    protected $description = 'Generate work schedule records for the next 6 months for all active work schedules';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        WorkSchedule::query()
            ->withWhereHas('assignments')
            ->belongToActiveTeam()
            ->each(fn($workSchedule) => GenerateWorkScheduleRecordsJob::dispatch($workSchedule));

        return self::SUCCESS;
    }
}

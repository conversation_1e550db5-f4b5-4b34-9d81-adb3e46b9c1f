<?php

namespace App\Services;

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use App\Models\Employee;
use App\Scopes\TenantScope;
use Carbon\Carbon;
use Carbon\CarbonImmutable;

class PrepareAttendanceUsingNewWorkScheduleFeatureService
{
    public function __construct(private Employee $employee)
    {
    }

    public function handle(): ?Attendance
    {
        $today = CarbonImmutable::now();

        $workScheduleRecord = $this->employee->workScheduleRecords()->date($today);

        $status = AttendanceStatus::fromWorkdayType($workScheduleRecord->workday_type);

        $workday = $workScheduleRecord->workday;

        return Attendance::query()
            ->withoutGlobalScope(TenantScope::class)
            ->firstOrCreate(
                [
                    'employee_id' => $this->employee->id,
                    'date' => $today,
                ],
                [
                    'team_id' => $this->employee->team_id,
                    'work_schedule_record_id' => $workScheduleRecord->id,
                    'shift_from' => $workday->start_time,
                    'shift_to' => $workday->end_time,
                    'net_hours' => Carbon::parse('00:00'),
                    'status' => $status->value,
                    'is_weekend' => $status === AttendanceStatus::WEEKEND,
                    'is_holiday' => $status === AttendanceStatus::HOLIDAY,
                    'active_until' => $workday->prevent_checkout_after,
                    'flexible_hours_before' => $workday->flexible_hours_before,
                    'flexible_hours_after' => $workday->flexible_hours_after,
                    'employee_statement_enabled' =>
                        $this->employee->team->employee_statement_config->enabled,
                ]
            );
    }
}

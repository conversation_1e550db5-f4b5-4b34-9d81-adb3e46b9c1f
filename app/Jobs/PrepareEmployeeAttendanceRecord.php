<?php

namespace App\Jobs;

use App\Enums\CheckoutReminderConfig;
use App\Models\Attendance;
use App\Models\Employee;
use App\Notifications\CheckoutReminderNotification;
use App\Services\PrepareAttendanceUsingOldShiftFeatureService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PrepareEmployeeAttendanceRecord implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(private readonly Employee $employee)
    {
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $attendance =
            $this->employee->team->enab ??
            (new PrepareAttendanceUsingOldShiftFeatureService($this->employee))->handle();

        if (!$attendance) {
            return;
        }

        if ($attendance->status !== Attendance::YET) {
            return;
        }

        $this->scheduleCheckoutReminder($attendance->shift_to);

        if ($attendance->employee_statement_enabled) {
            SendEmployeeStatementIfApplicableJob::dispatch($attendance)->delay(
                $attendance->shift_to
            );
        }
    }

    public function scheduleCheckoutReminder(Carbon $shiftTo): void
    {
        if (
            $this->employee->team->checkout_reminder_config === CheckoutReminderConfig::ByShiftEnd
        ) {
            $this->employee->notify(
                (new CheckoutReminderNotification())->delay($shiftTo->subMinutes(15))
            );
        }
    }
}
